#!/usr/bin/env python3
"""
Test script for Paimon improvements
Tests the new functionality without requiring Discord connection
"""

import sys
import os

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_ur_plus_query():
    """Test UR+ character queries"""
    print("🧪 Testing UR+ Character Queries...")
    
    try:
        from game_context import GameContext
        from chat_handler import <PERSON>t<PERSON><PERSON><PERSON>
        
        # Mock some UR+ characters for testing
        mock_characters = [
            {"Name": "<PERSON><PERSON><PERSON>", "Source": "Hunter x Hunter", "rarity": "UR+"},
            {"Name": "Midna (True Form)", "Source": "Zelda", "rarity": "UR+"},
            {"Name": "Sinbad", "Source": "Magi", "rarity": "UR+"},
        ]
        
        context = GameContext()
        context.all_characters = mock_characters  # Override with mock data
        handler = <PERSON><PERSON><PERSON><PERSON><PERSON>(context)
        
        # Test UR+ query
        response = handler.handle_message("Hey @p(AI)mon who is UR+ for this game?", "TestUser")
        print(f"   ✅ UR+ Query Response: {response[:100]}...")
        
        # Test other rarity queries
        test_queries = [
            "What SSR characters are there?",
            "Show me all UR+ cards",
            "Who are the SR characters?"
        ]
        
        for query in test_queries:
            response = handler.handle_message(query, "TestUser")
            status = "✅" if response and "characters" in response.lower() else "⚠️"
            print(f"   {status} '{query}' -> {'Response' if response else 'No response'}")
        
        print("   ✅ UR+ query tests completed!")
        return True
        
    except Exception as e:
        print(f"   ❌ UR+ query test failed: {e}")
        return False

def test_tied_leaderboard():
    """Test tied score handling"""
    print("🧪 Testing Tied Leaderboard Logic...")
    
    try:
        from game_context import GameContext
        from chat_handler import ChatHandler
        
        context = GameContext()
        # Mock tied players
        context.players = {
            1: {'display_name': 'Aloof', 'score': 5, 'marked_cells': set()},
            2: {'display_name': 'bongdrei', 'score': 5, 'marked_cells': set()},
            3: {'display_name': 'Player3', 'score': 3, 'marked_cells': set()},
        }
        
        handler = ChatHandler(context)
        
        # Test tied leaderboard query
        response = handler.handle_message("who is in the lead right now?", "TestUser")
        print(f"   ✅ Tied Leaderboard Response: {response}")
        
        # Check if it properly identifies the tie
        if "tie" in response.lower() and "aloof" in response and "bongdrei" in response:
            print("   ✅ Correctly identified tie!")
        else:
            print("   ⚠️ May not have properly identified tie")
        
        print("   ✅ Tied leaderboard tests completed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Tied leaderboard test failed: {e}")
        return False

def test_upload_detection():
    """Test upload detection logic"""
    print("🧪 Testing Upload Detection...")
    
    try:
        from game_context import GameContext
        from notification_engine import NotificationEngine
        
        context = GameContext()
        engine = NotificationEngine(context)
        
        # Mock a UR+ upload claim
        mock_claim = {
            'user_id': 1,
            'display_name': 'TestUser',
            'character': {
                'Name': 'Illumi Zoldyck',
                'rarity': 'UR+'
            },
            'cell_index': 5,
            'timestamp': 1234567890,
            'has_upload': True,
            'is_upload_only': False
        }
        
        # Test UR+ claim processing
        announcement = engine._process_new_claim(mock_claim, 1234567890)
        
        if announcement:
            print(f"   ✅ UR+ Announcement: {announcement['message']}")
            if 'LEGENDARY' in announcement['message'] or 'Ultra Rare Plus' in announcement['message']:
                print("   ✅ UR+ properly emphasized!")
            else:
                print("   ⚠️ UR+ may not be properly emphasized")
        else:
            print("   ❌ No announcement generated for UR+ claim!")
            return False
        
        # Test upload-only claim
        mock_claim['is_upload_only'] = True
        announcement = engine._process_new_claim(mock_claim, 1234567890)
        
        if announcement and 'uploaded proof' in announcement['message']:
            print("   ✅ Upload-only announcement works!")
        else:
            print("   ⚠️ Upload-only announcement may not work properly")
        
        print("   ✅ Upload detection tests completed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Upload detection test failed: {e}")
        return False

def main():
    """Run all improvement tests"""
    print("🌟 Testing Paimon Improvements")
    print("=" * 50)
    
    tests = [
        test_ur_plus_query,
        test_tied_leaderboard,
        test_upload_detection,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Improvement Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All improvements working correctly!")
        print("\n🚀 Ready to test with Discord:")
        print("1. Ask: 'Hey @p(AI)mon who is UR+ for this game?'")
        print("2. Upload a UR+ character to trigger announcements")
        print("3. Check leaderboard with tied scores")
        return True
    else:
        print("❌ Some improvements need attention.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
